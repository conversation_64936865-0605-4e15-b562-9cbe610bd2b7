---
import '../styles/global.css'
import Navigation from '../components/Navigation.astro'
import Hero from '../components/Hero.astro'
import Section from '../components/Section.astro'
import Gallery from '../components/Gallery.astro'
import Button from '../components/Button.astro'
import Footer from '../components/Footer.astro'
import FixedBackground from '../components/FixedBackground.astro'
import BackgroundOverlay from '../components/BackgroundOverlay.astro'
import Analytics from '../components/Analytics.astro'
import { getTranslations, useTranslations } from '../i18n/utils.ts'

const lang = 'en'
const translations = await getTranslations(lang)
const t = useTranslations(translations)

// Gallery images data
const galleryImages = [
  {
    src: `${import.meta.env.BASE_URL}beach_bar.jpeg`,
    alt: "Beach Bar Joni - Beachfront View"
  },
  {
    src: `${import.meta.env.BASE_URL}beach_bar_1.jpeg`,
    alt: "Beach Bar Joni - Outdoor Seating"
  },
  {
    src: `${import.meta.env.BASE_URL}beach_bar_2.jpeg`,
    alt: "Beach Bar Joni - Bar Interior"
  },
  {
    src: `${import.meta.env.BASE_URL}drinks.jpeg`,
    alt: "Beach Bar Joni - Cocktails and Drinks"
  }
];
---

<html lang={lang}>
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<meta name="generator" content={Astro.generator} />
		<title>{t('meta.title')}</title>
		<meta name="description" content={t('meta.description')} />

		<!-- Critical performance optimizations -->
		<link rel="preload" href={`${import.meta.env.BASE_URL}beach_bar.webp`} as="image" type="image/webp" fetchpriority="high" />
		<link rel="preload" href={`${import.meta.env.BASE_URL}beach_bar.jpeg`} as="image" type="image/jpeg" fetchpriority="high" />

		<!-- Preload critical gallery images -->
		<link rel="preload" href={`${import.meta.env.BASE_URL}beach_bar_1.webp`} as="image" type="image/webp" />
		<link rel="preload" href={`${import.meta.env.BASE_URL}beach_bar_2.webp`} as="image" type="image/webp" />

		<!-- DNS prefetch for external resources -->
		<link rel="dns-prefetch" href="//script.google.com" />
		<link rel="dns-prefetch" href="//www.instagram.com" />
		<link rel="preconnect" href="//script.google.com" crossorigin />

		<!-- Analytics Scripts -->
		<Analytics />

		<!-- Resource hints for better loading -->
		<link rel="preload" href={`${import.meta.env.BASE_URL}assets/page.Cg2IRjKt.js`} as="script" />
		<link rel="preload" href={`${import.meta.env.BASE_URL}assets/index.ClXsWmnx.css`} as="style" />

		<!-- Favicons -->
		<link rel="icon" type="image/x-icon" href={`${import.meta.env.BASE_URL}favicon.ico`} />
		<link rel="icon" type="image/png" sizes="16x16" href={`${import.meta.env.BASE_URL}favicon-16x16.png`} />
		<link rel="icon" type="image/png" sizes="32x32" href={`${import.meta.env.BASE_URL}favicon-32x32.png`} />
		<link rel="apple-touch-icon" sizes="180x180" href={`${import.meta.env.BASE_URL}apple-touch-icon.png`} />

		<!-- Theme color for mobile browsers -->
		<meta name="theme-color" content="#0d9488" />
		<meta name="msapplication-TileColor" content="#0d9488" />

		<!-- Cache control -->
		<meta http-equiv="Cache-Control" content="public, max-age=31536000" />
	</head>
	<body class="bg-white">
		<!-- Fixed Background -->
		<FixedBackground src="beach_bar.jpeg" />

		<!-- Background Overlay for transparency effects -->
		<BackgroundOverlay />

		<!-- Main Content Container -->
		<div class="z-10 relative">
			<!-- Navigation -->
			<Navigation currentPage="home" lang={lang} translations={translations} />

			<main>
				<!-- Hero Section -->
				<Hero
					title={t('hero.title')}
					subtitle={t('hero.subtitle')}
					showButtons={true}
					lang={lang}
					translations={translations}
					useFixedBackground={true}
				/>

				<!-- Gallery Section -->
				<Section
					id="gallery"
					background="semi-transparent"
				>
					<Gallery images={galleryImages} />
				</Section>

				<!-- Location Section -->
				<Section
					id="location"
					title={t('location.title')}
					subtitle={t('location.subtitle')}
					background="semi-transparent"
				>
			<div class="items-start gap-12 grid grid-cols-1 lg:grid-cols-2">
				<!-- Info Cards -->
				<div class="space-y-8">
					<div class="bg-white shadow-md hover:shadow-lg p-6 rounded-xl transition-shadow">
						<h3 class="mb-3 font-semibold text-gray-900 text-xl">{t('location.address')}</h3>
						<p class="text-gray-600 leading-relaxed" set:html={t('location.addressText').replace(/\n/g, '<br>')}></p>
					</div>
					<div class="bg-white shadow-md hover:shadow-lg p-6 rounded-xl transition-shadow">
						<h3 class="mb-3 font-semibold text-gray-900 text-xl">{t('location.hours')}</h3>
						<p class="text-gray-600 leading-relaxed" set:html={t('location.hoursText').replace(/\n/g, '<br>')}></p>
					</div>

				</div>

				<!-- Google Maps -->
				<div class="bg-white shadow-md hover:shadow-lg rounded-xl overflow-hidden transition-shadow">
					<iframe
						src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3048.8234567890123!2d19.52006358162717!3d41.25247109878686!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x134fdfe5c479096b%3A0x14f9543b2e829c61!2sBar%20Joni!5e0!3m2!1sen!2s!4v1234567890123!5m2!1sen!2s"
						width="100%"
						height="300"
						style="border:0;"
						allowfullscreen=""
						loading="lazy"
						referrerpolicy="no-referrer-when-downgrade"
						title="Bar Joni Location">
					</iframe>
				</div>
			</div>
		</Section>

				<!-- Contact Section -->
				<Section
					id="contact"
					background="semi-transparent"
				>
			<div class="space-y-8 mx-auto max-w-4xl text-center">
				<p class="text-gray-700 text-lg leading-relaxed">
				</p>
				<div class="flex sm:flex-row flex-col justify-center gap-4">
					<Button
						href="https://www.instagram.com/beach_bar_joni/"
						target="_blank"
						rel="noopener"
						variant="primary"
						size="lg"
					>
						<span class="mr-2">📱</span>
						{t('contact.followInstagram')}
					</Button>
					<Button
						href="https://www.google.com/maps/place/Bar+Joni/@41.2524711,19.5200636,430m/data=!3m1!1e3!4m6!3m5!1s0x134fdfe5c479096b:0x14f9543b2e829c61!8m2!3d41.2524711!4d19.5200636!16s%2Fg%2F11y3k8qx8q"
						target="_blank"
						rel="noopener"
						variant="outline"
						size="lg"
					>
						<span class="mr-2">📍</span>
						{t('contact.getDirections')}
					</Button>
				</div>
			</div>
				</Section>
			</main>

			<!-- Footer -->
			<Footer lang={lang} translations={translations} />
		</div>



		<!-- Highly Optimized Visitor Tracking Script -->
		<script>
			// Ultra-lightweight visitor tracking with minimal performance impact
			function trackVisitor() {
				// Use requestIdleCallback for zero impact on main thread
				const track = () => {
					try {
						const scriptUrl = 'https://script.google.com/macros/s/AKfycbxvZNXq1f3CU8pEg-pgsij9VICglp62bLKH1l0YRPe4k4GdOFvvLaA0rmNQ2XzqrPRz0Q/exec';

						// Minimal data collection for better performance
						const params = new URLSearchParams({
							url: location.pathname,
							ref: document.referrer ? new URL(document.referrer).hostname : 'direct',
							ts: Date.now().toString()
						});

						// Use image pixel method - fastest and most reliable
						const img = new Image();
						img.src = `${scriptUrl}?${params}`;

						// Clean up after 5 seconds
						setTimeout(() => img.src = '', 5000);
					} catch (e) {
						// Silently fail to avoid any performance impact
					}
				};

				// Use requestIdleCallback if available, otherwise setTimeout
				if ('requestIdleCallback' in window) {
					requestIdleCallback(track, { timeout: 2000 });
				} else {
					setTimeout(track, 100);
				}
			}

			// Track visitor after page load to not block rendering
			if (document.readyState === 'complete') {
				trackVisitor();
			} else {
				window.addEventListener('load', trackVisitor, { once: true });
			}

			// Register service worker for performance improvements
			if ('serviceWorker' in navigator) {
				window.addEventListener('load', () => {
					navigator.serviceWorker.register('/sw.js')
						.then(registration => {
							console.log('SW registered: ', registration);
						})
						.catch(registrationError => {
							console.log('SW registration failed: ', registrationError);
						});
				});
			}
		</script>
	</body>
</html>
